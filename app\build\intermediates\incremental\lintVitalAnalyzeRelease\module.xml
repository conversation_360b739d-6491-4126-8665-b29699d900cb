<lint-module
    format="1"
    dir="C:\Users\<USER>\StudioProjects\link-eye\app"
    name=":app"
    type="APP"
    maven="link-eye:app:unspecified"
    agpVersion="8.4.1"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
