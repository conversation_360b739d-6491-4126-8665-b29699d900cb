-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:2:1-29:12
INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:2:1-29:12
INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:2:1-29:12
INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:2:1-29:12
MERGED from [ijkplayer-cmake-release.aar] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\5b5144046830bca15df79d4088f2b207\transformed\ijkplayer-cmake-release\AndroidManifest.xml:2:1-7:12
	package
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:3:5-34
		INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:5-80
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:22-77
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:5-71
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:22-68
uses-feature#android.software.leanback
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:8:5-87
	android:required
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:8:60-84
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:8:19-59
uses-feature#android.hardware.touchscreen
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:5-90
	android:required
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:63-87
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:19-62
application
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:11:5-28:19
INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:11:5-28:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml
	android:label
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:12:9-33
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:14:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:13:9-35
	android:theme
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:17:9-40
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:15:9-69
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:16:9-44
activity#com.example.linkeye.MainActivity
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:18:9-27:20
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:20:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:21:13-74
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:19:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:22:13-26:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:23:17-69
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:23:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:24:17-77
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:24:27-74
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:25:17-86
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:25:27-83
uses-sdk
INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml
MERGED from [ijkplayer-cmake-release.aar] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\5b5144046830bca15df79d4088f2b207\transformed\ijkplayer-cmake-release\AndroidManifest.xml:5:5-44
MERGED from [ijkplayer-cmake-release.aar] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\5b5144046830bca15df79d4088f2b207\transformed\ijkplayer-cmake-release\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml
