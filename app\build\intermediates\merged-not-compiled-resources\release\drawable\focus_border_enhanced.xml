<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Subtle outer glow - very thin for minimal visual impact -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <stroke
                android:width="1dp"
                android:color="@color/focus_border_glow" />
            <corners android:radius="2dp" />
        </shape>
    </item>

    <!-- Ultra-thin main border - barely visible but maintains contrast -->
    <item
        android:left="1dp"
        android:top="1dp"
        android:right="1dp"
        android:bottom="1dp">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <stroke
                android:width="0.5dp"
                android:color="@color/focus_border_main" />
            <corners android:radius="1dp" />
        </shape>
    </item>
    
</layer-list>
